"""
文档处理器
用于处理HAZOP输入文件和生成输出报告
"""

import os
import re
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class EquipmentInfo:
    """设备信息数据类"""
    tag: str  # 设备标号
    name: str  # 设备名称
    type: str  # 设备类型
    specifications: Dict[str, str]  # 规格参数
    operating_conditions: Dict[str, str]  # 操作条件


@dataclass
class MaterialInfo:
    """物料信息数据类"""
    name: str  # 物料名称
    cas_number: str  # CAS号
    composition: Dict[str, float]  # 组成
    physical_properties: Dict[str, str]  # 物理性质
    safety_properties: Dict[str, str]  # 安全性质
    hazard_classification: List[str]  # 危险性分类


@dataclass
class ProcessData:
    """工艺数据类"""
    equipment_list: List[EquipmentInfo]
    materials: List[MaterialInfo]
    operating_conditions: Dict[str, str]
    control_systems: Dict[str, str]
    safety_systems: Dict[str, str]
    process_description: str


class DocumentProcessor:
    """文档处理器类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 设备类型映射
        self.equipment_type_map = {
            'D': '储罐',
            'P': '泵',
            'LV': '液位调节阀',
            'FV': '流量调节阀', 
            'PV': '压力调节阀',
            'XV': '切断阀',
            'CV': '止回阀',
            'HV': '手动阀',
            'PSV': '安全阀',
            'LT': '液位变送器',
            'FT': '流量变送器',
            'PT': '压力变送器',
            'LICA': '液位控制器',
            'FICA': '流量控制器',
            'PICA': '压力控制器'
        }
        
        # 物料属性模式
        self.material_patterns = {
            'cas_number': r'CAS\s*[：:]\s*(\d{2,7}-\d{2}-\d)',
            'flash_point': r'闪点\s*[：:]\s*(-?\d+(?:\.\d+)?)\s*[℃°C]',
            'boiling_point': r'沸点\s*[：:]\s*(-?\d+(?:\.\d+)?)\s*[℃°C]',
            'density': r'密度\s*[：:]\s*(\d+(?:\.\d+)?)\s*kg/m³',
            'explosion_limit': r'爆炸极限\s*[：:]\s*([\d.]+\s*[~-]\s*[\d.]+)\s*(?:vol%|%)',
            'auto_ignition': r'自燃温度\s*[：:]\s*(\d+)\s*[℃°C]'
        }
    
    def read_input_file(self, file_path: str) -> str:
        """
        读取输入文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容
        """
        try:
            # 尝试不同的编码方式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    self.logger.info(f"成功使用 {encoding} 编码读取文件: {file_path}")
                    return content
                except UnicodeDecodeError:
                    continue
            
            raise Exception("无法使用支持的编码读取文件")
            
        except FileNotFoundError:
            raise Exception(f"文件不存在: {file_path}")
        except Exception as e:
            raise Exception(f"读取文件失败: {str(e)}")
    
    def parse_input_data(self, content: str) -> ProcessData:
        """
        解析输入数据
        
        Args:
            content: 文件内容
            
        Returns:
            解析后的工艺数据
        """
        try:
            # 提取设备信息
            equipment_list = self._extract_equipment_info(content)
            
            # 提取物料信息
            materials = self._extract_material_info(content)
            
            # 提取操作条件
            operating_conditions = self._extract_operating_conditions(content)
            
            # 提取控制系统信息
            control_systems = self._extract_control_systems(content)
            
            # 提取安全系统信息
            safety_systems = self._extract_safety_systems(content)
            
            # 生成工艺描述
            process_description = self._generate_process_description(content)
            
            return ProcessData(
                equipment_list=equipment_list,
                materials=materials,
                operating_conditions=operating_conditions,
                control_systems=control_systems,
                safety_systems=safety_systems,
                process_description=process_description
            )
            
        except Exception as e:
            self.logger.error(f"解析输入数据失败: {str(e)}")
            raise Exception(f"数据解析失败: {str(e)}")
    
    def _extract_equipment_info(self, content: str) -> List[EquipmentInfo]:
        """提取设备信息"""
        equipment_list = []
        
        # 使用正则表达式匹配设备标号
        equipment_pattern = r'([A-Z]+[-]?\d{4,5}(?:-\d)?)'
        matches = re.findall(equipment_pattern, content)
        
        for tag in set(matches):  # 去重
            # 确定设备类型
            equipment_type = self._determine_equipment_type(tag)
            
            # 提取设备规格和操作条件
            specifications, operating_conditions = self._extract_equipment_details(content, tag)
            
            equipment_info = EquipmentInfo(
                tag=tag,
                name=f"{equipment_type}_{tag}",
                type=equipment_type,
                specifications=specifications,
                operating_conditions=operating_conditions
            )
            equipment_list.append(equipment_info)
        
        return equipment_list
    
    def _determine_equipment_type(self, tag: str) -> str:
        """确定设备类型"""
        for prefix, eq_type in self.equipment_type_map.items():
            if tag.startswith(prefix):
                return eq_type
        return "未知设备"
    
    def _extract_equipment_details(self, content: str, tag: str) -> Tuple[Dict[str, str], Dict[str, str]]:
        """提取设备详细信息"""
        specifications = {}
        operating_conditions = {}
        
        # 在设备标号附近寻找相关参数
        tag_pattern = rf'{re.escape(tag)}[^\n]*'
        matches = re.findall(tag_pattern, content)
        
        for match in matches:
            # 提取数值和单位
            # 温度
            temp_match = re.search(r'(\d+(?:\.\d+)?)\s*[℃°C]', match)
            if temp_match:
                operating_conditions['温度'] = temp_match.group(0)
            
            # 压力
            pressure_match = re.search(r'(\d+(?:\.\d+)?)\s*(bar|kPa|MPa)', match)
            if pressure_match:
                operating_conditions['压力'] = pressure_match.group(0)
            
            # 流量
            flow_match = re.search(r'(\d+(?:\.\d+)?)\s*(t/h|kg/h|m³/h)', match)
            if flow_match:
                operating_conditions['流量'] = flow_match.group(0)
        
        return specifications, operating_conditions
    
    def _extract_material_info(self, content: str) -> List[MaterialInfo]:
        """提取物料信息"""
        materials = []
        
        # 识别物料名称
        material_names = {
            '正丁醇': ['正丁醇', 'butanol', '1-butanol'],
            '正丁醛': ['正丁醛', 'butyraldehyde', 'butanal'],
            '苯酚': ['苯酚', 'phenol'],
            '氮气': ['氮气', 'nitrogen', 'N2']
        }
        
        for material_name, keywords in material_names.items():
            for keyword in keywords:
                if keyword.lower() in content.lower():
                    # 提取物料属性
                    material_info = self._extract_material_properties(content, material_name)
                    if material_info:
                        materials.append(material_info)
                    break
        
        return materials
    
    def _extract_material_properties(self, content: str, material_name: str) -> Optional[MaterialInfo]:
        """提取特定物料的属性"""
        try:
            # 提取CAS号
            cas_number = ""
            cas_match = re.search(self.material_patterns['cas_number'], content)
            if cas_match:
                cas_number = cas_match.group(1)
            
            # 提取物理性质
            physical_properties = {}
            for prop_name, pattern in self.material_patterns.items():
                if prop_name != 'cas_number':
                    match = re.search(pattern, content)
                    if match:
                        physical_properties[prop_name] = match.group(1)
            
            # 提取组成信息
            composition = self._extract_composition(content, material_name)
            
            # 提取安全性质
            safety_properties = self._extract_safety_properties(content)
            
            # 确定危险性分类
            hazard_classification = self._determine_hazard_classification(physical_properties, safety_properties)
            
            return MaterialInfo(
                name=material_name,
                cas_number=cas_number,
                composition=composition,
                physical_properties=physical_properties,
                safety_properties=safety_properties,
                hazard_classification=hazard_classification
            )
            
        except Exception as e:
            self.logger.warning(f"提取物料 {material_name} 属性失败: {str(e)}")
            return None
    
    def _extract_composition(self, content: str, material_name: str) -> Dict[str, float]:
        """提取物料组成"""
        composition = {}
        
        # 查找百分比组成
        comp_pattern = r'([^：:]+)\s*(\d+(?:\.\d+)?)\s*(?:wt%|%)'
        matches = re.findall(comp_pattern, content)
        
        for component, percentage in matches:
            component = component.strip()
            if component and material_name in component or component in material_name:
                composition[component] = float(percentage)
        
        return composition
    
    def _extract_safety_properties(self, content: str) -> Dict[str, str]:
        """提取安全性质"""
        safety_properties = {}
        
        # LC50
        lc50_match = re.search(r'LC50[：:]\s*([^，,\n]+)', content)
        if lc50_match:
            safety_properties['LC50'] = lc50_match.group(1).strip()
        
        # IDLH
        idlh_match = re.search(r'IDLH[：:]\s*(\d+)\s*ppm', content)
        if idlh_match:
            safety_properties['IDLH'] = idlh_match.group(1) + ' ppm'
        
        return safety_properties
    
    def _determine_hazard_classification(self, physical_props: Dict, safety_props: Dict) -> List[str]:
        """确定危险性分类"""
        classifications = []
        
        # 根据闪点判断易燃性
        if 'flash_point' in physical_props:
            flash_point = float(physical_props['flash_point'])
            if flash_point < 23:
                classifications.append('极易燃液体')
            elif flash_point < 61:
                classifications.append('易燃液体')
        
        # 根据毒性参数判断毒性
        if 'IDLH' in safety_props:
            idlh_value = int(re.search(r'\d+', safety_props['IDLH']).group())
            if idlh_value < 100:
                classifications.append('高毒性')
            elif idlh_value < 1000:
                classifications.append('中等毒性')
        
        return classifications
    
    def _extract_operating_conditions(self, content: str) -> Dict[str, str]:
        """提取操作条件"""
        conditions = {}
        
        # 提取温度范围
        temp_matches = re.findall(r'(\d+)\s*[℃°C]', content)
        if temp_matches:
            temps = [int(t) for t in temp_matches]
            conditions['操作温度范围'] = f"{min(temps)}-{max(temps)}°C"
        
        # 提取压力范围
        pressure_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(bar|kPa|MPa)', content)
        if pressure_matches:
            conditions['压力条件'] = str(pressure_matches)
        
        return conditions
    
    def _extract_control_systems(self, content: str) -> Dict[str, str]:
        """提取控制系统信息"""
        control_systems = {}
        
        # 液位控制
        lica_matches = re.findall(r'(LICA-\d{5})', content)
        if lica_matches:
            control_systems['液位控制'] = ', '.join(set(lica_matches))
        
        # 流量控制
        fica_matches = re.findall(r'(FICA-\d{5})', content)
        if fica_matches:
            control_systems['流量控制'] = ', '.join(set(fica_matches))
        
        # 压力控制
        pica_matches = re.findall(r'(PICA-\d{5})', content)
        if pica_matches:
            control_systems['压力控制'] = ', '.join(set(pica_matches))
        
        return control_systems
    
    def _extract_safety_systems(self, content: str) -> Dict[str, str]:
        """提取安全系统信息"""
        safety_systems = {}
        
        # 安全阀
        psv_matches = re.findall(r'(PSV-\d{5})', content)
        if psv_matches:
            safety_systems['安全阀'] = ', '.join(set(psv_matches))
        
        # 联锁系统
        if '联锁' in content:
            safety_systems['联锁系统'] = '已配置'
        
        # 报警系统  
        if '报警' in content:
            safety_systems['报警系统'] = '已配置'
        
        return safety_systems
    
    def _generate_process_description(self, content: str) -> str:
        """生成工艺描述"""
        # 提取关键信息生成描述
        description_parts = []
        
        if '储罐' in content:
            description_parts.append("储罐系统")
        
        # 识别工艺流程
        if '进料' in content and '出料' in content:
            description_parts.append("包含进料和出料流程")
        
        if '控制' in content:
            description_parts.append("配置自动控制系统")
        
        if not description_parts:
            description_parts.append("工业储罐系统")
        
        return "，".join(description_parts) + "的HAZOP安全分析"
    
    def save_analysis_results(self, results: str, output_path: str) -> None:
        """
        保存分析结果
        
        Args:
            results: 分析结果
            output_path: 输出路径
        """
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 添加生成时间戳
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            header = f"# HAZOP分析报告\n\n生成时间: {timestamp}\n\n"
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(header + results)
            
            self.logger.info(f"分析结果已保存到: {output_path}")
            
        except Exception as e:
            raise Exception(f"保存结果失败: {str(e)}")
    
    def export_to_json(self, process_data: ProcessData, output_path: str) -> None:
        """
        导出数据为JSON格式
        
        Args:
            process_data: 工艺数据
            output_path: 输出路径
        """
        try:
            data_dict = asdict(process_data)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data_dict, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"数据已导出为JSON格式: {output_path}")
            
        except Exception as e:
            raise Exception(f"JSON导出失败: {str(e)}")
    
    def get_summary(self, process_data: ProcessData) -> str:
        """
        生成数据摘要
        
        Args:
            process_data: 工艺数据
            
        Returns:
            数据摘要
        """
        summary = []
        summary.append(f"设备数量: {len(process_data.equipment_list)}")
        summary.append(f"物料种类: {len(process_data.materials)}")
        summary.append(f"控制系统: {len(process_data.control_systems)}")
        summary.append(f"安全系统: {len(process_data.safety_systems)}")
        summary.append(f"工艺描述: {process_data.process_description}")
        
        return "\n".join(summary)