# HAZOP AI Agent

基于LangChain框架和Google Gemini的智能HAZOP（危险性与可操作性）分析系统，专门用于储罐系统的安全风险评估。

## 🎯 项目特点

- 🤖 **智能分析**: 基于Google Gemini 2.5 Pro模型的AI驱动分析
- 📊 **标准化流程**: 遵循工业HAZOP分析标准和方法论
- 🔧 **自动化处理**: 自动解析输入文件，生成专业报告
- 🛡️ **输入验证**: 全面的数据验证和错误检查
- 📝 **专业报告**: 生成符合工业标准的Markdown格式报告
- 🔄 **批量处理**: 支持多文件批量分析

## 🏗️ 系统架构

```
HAZOP_AI/
├── src/
│   ├── hazop_agent.py          # 主要的HAZOP AI代理
│   ├── document_processor.py   # 文档处理器
│   ├── prompts/
│   │   ├── hazop_analysis.py   # HAZOP分析提示词
│   │   └── report_generation.py # 报告生成提示词
│   └── utils/
│       ├── gemini_client.py    # Google Gemini客户端
│       └── validators.py       # 输入验证工具
├── refrences/                  # 参考文档和分析逻辑
├── input/                      # 输入文件目录
├── output/                     # 输出报告目录
├── tests/                      # 测试用例
├── config.py                   # 配置管理
├── requirements.txt            # 项目依赖
└── example_usage.py           # 使用示例
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境

复制环境变量配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置你的Google API密钥：
```bash
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_MODEL=gemini-2.5-pro
```

### 3. 运行示例

```bash
python example_usage.py
```

### 4. 命令行使用

单文件分析：
```bash
python src/hazop_agent.py \
  --input "refrences/储罐分析 原方案.txt" \
  --logic "refrences/储罐高流量HAZOP分析逻辑.md" \
  --output "output/hazop_report.md"
```

批量分析：
```bash
python src/hazop_agent.py \
  --input "input/" \
  --logic "refrences/储罐高流量HAZOP分析逻辑.md" \
  --output "output/" \
  --batch
```

## 📋 功能特点

### HAZOP分析功能

- **高流量偏差分析**: 
  - 自动应用4倍流量规则进行故障分析
  - 定量计算满液时间、泄漏量等关键参数
  - 全面的后果评估和风险分析

- **低流量偏差分析**:
  - 设备断料和空罐风险评估
  - 泵干转保护分析
  - 下游设备影响评估

- **物料危险性分析**:
  - 火灾爆炸风险评估
  - 毒性危害分析
  - 环境影响评估

- **保护层分析**:
  - 基本控制系统(BPCS)评估
  - 安全仪表系统(SIS)分析
  - 物理保护层识别

### 技术特点

- **智能解析**: 自动识别设备标号、物料属性、操作条件
- **格式灵活**: 支持多种编码格式的中文文件
- **错误处理**: 完善的异常处理和日志记录
- **可扩展性**: 模块化设计，易于扩展新功能

## 📖 使用指南

### 输入文件格式

输入文件应包含以下信息：

```
储罐系统信息
设备清单:
- D1001: 储罐
- LICA-10001: 液位控制器  
- LV-10001: 液位调节阀

物料信息:
- 正丁醇: 95wt%, CAS: 123-72-8
- 操作温度: 40°C
- 操作压力: 1 barG
- 正常流量: 5 t/h

控制系统:
- 液位控制回路
- 报警联锁系统
```

### 分析逻辑文件

参考逻辑文件定义了HAZOP分析的方法论和计算规则，例如：

```markdown
# 储罐高流量HAZOP分析逻辑

## 关键计算规则
- 故障流量 = 正常流量 × 4
- 满液时间 = 剩余容积 / 净流入速率

## 风险评估维度
- 火灾爆炸风险
- 毒性风险  
- 设备损坏风险
```

### 输出报告格式

生成的报告包含：

```markdown
# 储罐系统HAZOP分析报告

## 执行摘要
- 分析目的和范围
- 主要发现
- 关键建议

## HAZOP分析结果
### 高流量偏差分析
- 原因: LICA-10001故障，LV-10001开大...
- 后果: 故障流量20t/h，满液时间30分钟...
- 有效保护层: LIAS-10003高高液位联锁...

### 低流量偏差分析
[详细分析内容]

## 风险评估总结
## 结论与建议
```

## 🧪 测试

运行测试用例：
```bash
pytest tests/ -v
```

运行特定测试：
```bash
pytest tests/test_hazop_agent.py::TestHAZOPAgent::test_agent_initialization -v
```

## 📊 性能优化

- **批量处理**: 支持多文件并行分析
- **缓存机制**: 减少重复的API调用
- **错误重试**: 自动重试失败的分析请求
- **内存管理**: 大文件分块处理

## 🔧 配置选项

主要配置参数：

```python
config = HAZOPAnalysisConfig(
    api_key="your_api_key",           # Google API密钥
    model_name="gemini-2.5-pro",      # AI模型
    max_retries=3,                    # 最大重试次数
    temperature=0.1,                  # AI响应温度
    enable_validation=True,           # 启用输入验证
    output_format="markdown"          # 输出格式
)
```

## 🚨 注意事项

1. **API配额**: 注意Google Gemini API的使用配额限制
2. **文件编码**: 确保输入文件使用UTF-8编码
3. **网络连接**: 需要稳定的网络连接访问Google API
4. **数据安全**: 敏感数据在分析前请做脱敏处理

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件至项目维护者
- 查看项目文档和示例

## 🔄 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🤖 集成Google Gemini AI模型
- 📊 实现完整HAZOP分析流程
- 🛡️ 添加输入验证功能
- 📝 支持专业报告生成
- 🔄 支持批量分析处理

---

**注意**: 本系统仅作为HAZOP分析的辅助工具，最终的安全决策应由专业的安全工程师进行确认和审查。