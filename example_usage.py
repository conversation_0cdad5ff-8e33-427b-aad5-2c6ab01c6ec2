"""
HAZOP AI Agent 使用示例
演示如何使用HAZOP分析代理进行储罐安全分析
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.hazop_agent import H<PERSON><PERSON>OPAgent, HAZOPAnalysisConfig
from config import CONFIG


def example_single_analysis():
    """单文件分析示例"""
    print("=" * 60)
    print("HAZOP AI Agent - 单文件分析示例")
    print("=" * 60)
    
    # 配置分析参数
    config = HAZOPAnalysisConfig(
        api_key=CONFIG.api.google_api_key,
        model_name=CONFIG.api.gemini_model,
        max_retries=3,
        enable_validation=True
    )
    
    # 创建HAZOP代理
    agent = HAZOPAgent(config)
    
    # 输入文件路径
    input_file = "refrences/储罐分析 原方案.txt"
    logic_file = "refrences/储罐高流量HAZOP分析逻辑.md"
    output_file = "output/example_hazop_report.md"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"❌ 输入文件不存在: {input_file}")
        return
    
    if not os.path.exists(logic_file):
        print(f"❌ 逻辑文件不存在: {logic_file}")
        return
    
    print(f"📁 输入文件: {input_file}")
    print(f"📁 逻辑文件: {logic_file}")
    print(f"📁 输出文件: {output_file}")
    print()
    
    try:
        # 执行分析
        print("🚀 开始HAZOP分析...")
        result = agent.analyze_tank_system(input_file, logic_file, output_file)
        
        if result['status'] == 'success':
            print("✅ 分析完成!")
            print(f"   📊 设备数量: {result['equipment_count']}")
            print(f"   🧪 物料数量: {result['material_count']}")
            print(f"   📝 报告长度: {result['report_length']} 字符")
            print(f"   ⏰ 分析时间: {result['analysis_time']}")
            print(f"   💾 输出文件: {result['output_file']}")
        else:
            print("❌ 分析失败!")
            print(f"   错误信息: {result['error_message']}")
            
    except Exception as e:
        print(f"❌ 程序执行错误: {str(e)}")


def example_batch_analysis():
    """批量分析示例"""
    print("=" * 60)
    print("HAZOP AI Agent - 批量分析示例")
    print("=" * 60)
    
    # 配置分析参数
    config = HAZOPAnalysisConfig(
        api_key=CONFIG.api.google_api_key,
        model_name=CONFIG.api.gemini_model,
        max_retries=2,  # 批量分析时减少重试次数
        enable_validation=True
    )
    
    # 创建HAZOP代理
    agent = HAZOPAgent(config)
    
    # 批量分析参数
    input_dir = "refrences"  # 包含多个分析文件的目录
    logic_file = "refrences/储罐高流量HAZOP分析逻辑.md"
    output_dir = "output/batch_results"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"📁 输入目录: {input_dir}")
    print(f"📁 逻辑文件: {logic_file}")
    print(f"📁 输出目录: {output_dir}")
    print()
    
    try:
        # 执行批量分析
        print("🚀 开始批量HAZOP分析...")
        results = agent.batch_analyze(input_dir, logic_file, output_dir)
        
        # 统计结果
        total_files = len(results)
        successful_files = sum(1 for r in results if r['status'] == 'success')
        failed_files = total_files - successful_files
        
        print(f"📊 批量分析结果:")
        print(f"   📂 总文件数: {total_files}")
        print(f"   ✅ 成功: {successful_files}")
        print(f"   ❌ 失败: {failed_files}")
        print()
        
        # 显示详细结果
        for i, result in enumerate(results, 1):
            if result['status'] == 'success':
                print(f"   {i}. ✅ {Path(result['input_file']).name}")
            else:
                print(f"   {i}. ❌ {Path(result['input_file']).name} - {result.get('error_message', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 批量分析执行错误: {str(e)}")


def example_agent_status():
    """代理状态检查示例"""
    print("=" * 60)
    print("HAZOP AI Agent - 状态检查示例")
    print("=" * 60)
    
    config = HAZOPAnalysisConfig(
        api_key=CONFIG.api.google_api_key,
        model_name=CONFIG.api.gemini_model
    )
    
    agent = HAZOPAgent(config)
    status = agent.get_agent_status()
    
    print("🔍 代理状态:")
    print(f"   🤖 AI模型: {status['gemini_model']}")
    print(f"   ✅ API配置: {'已配置' if status['api_configured'] else '未配置'}")
    print(f"   🔒 输入验证: {'启用' if status['validation_enabled'] else '禁用'}")
    print(f"   🔄 最大重试: {status['max_retries']}")
    print(f"   📄 输出格式: {status['output_format']}")


def example_validation_test():
    """输入验证测试示例"""
    print("=" * 60)
    print("HAZOP AI Agent - 输入验证测试")
    print("=" * 60)
    
    from src.utils.validators import HAZOPInputValidator
    
    validator = HAZOPInputValidator()
    
    # 测试有效输入
    valid_content = """
    储罐系统HAZOP分析数据
    设备清单:
    D1001: 储罐，容积10m³
    LICA-10001: 液位控制器
    LV-10001: 液位调节阀
    
    物料信息:
    正丁醇: 95wt%, 温度40°C, 压力1barG, 流量5t/h
    
    控制系统:
    液位控制回路已配置
    报警联锁系统正常
    """
    
    print("🧪 测试有效输入验证...")
    result = validator.validate_input_file(valid_content)
    
    print(f"   验证结果: {'✅ 通过' if result.is_valid else '❌ 失败'}")
    if result.errors:
        print(f"   错误数量: {len(result.errors)}")
        for error in result.errors:
            print(f"     - {error}")
    
    if result.warnings:
        print(f"   警告数量: {len(result.warnings)}")
        for warning in result.warnings[:3]:  # 只显示前3个警告
            print(f"     - {warning}")
    
    if result.extracted_data:
        print(f"   提取数据: {len(result.extracted_data)} 类")
    
    print()
    
    # 测试无效输入
    invalid_content = "这是一个无效的输入文件"
    
    print("🧪 测试无效输入验证...")
    result = validator.validate_input_file(invalid_content)
    
    print(f"   验证结果: {'✅ 通过' if result.is_valid else '❌ 失败'}")
    if result.errors:
        print(f"   错误数量: {len(result.errors)}")


def main():
    """主函数"""
    print("🤖 HAZOP AI Agent 使用示例")
    print("基于Google Gemini的智能HAZOP分析系统")
    print()
    
    # 检查配置
    if not CONFIG.api.google_api_key:
        print("⚠️  警告: Google API密钥未配置，将使用默认密钥")
    
    while True:
        print("\n请选择示例:")
        print("1. 单文件分析示例")
        print("2. 批量分析示例") 
        print("3. 代理状态检查")
        print("4. 输入验证测试")
        print("0. 退出")
        
        choice = input("\n请输入选项 (0-4): ").strip()
        
        if choice == "1":
            example_single_analysis()
        elif choice == "2":
            example_batch_analysis()
        elif choice == "3":
            example_agent_status()
        elif choice == "4":
            example_validation_test()
        elif choice == "0":
            print("👋 再见!")
            break
        else:
            print("❌ 无效选项，请重新选择")
        
        input("\n按Enter键继续...")


if __name__ == "__main__":
    main()