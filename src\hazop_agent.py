"""
HAZOP AI Agent - 主要的HAZOP分析代理
基于LangChain框架和Google Gemini的智能HAZOP分析系统
"""

import os
import sys
import argparse
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.gemini_client import GeminiClient
from src.utils.validators import HAZOPInputValidator, ValidationResult
from src.document_processor import DocumentProcessor, ProcessData
from src.prompts.hazop_analysis import HAZOPAnalysisPrompts
from src.prompts.report_generation import ReportGenerationPrompts


@dataclass
class HAZOPAnalysisConfig:
    """HAZOP分析配置"""
    api_key: str
    model_name: str = "gemini-2.5-pro"
    max_retries: int = 3
    temperature: float = 0.1
    enable_validation: bool = True
    output_format: str = "markdown"  # markdown, json, html


class HAZOPAgent:
    """HAZOP AI代理主类"""
    
    def __init__(self, config: HAZOPAnalysisConfig):
        """
        初始化HAZOP代理
        
        Args:
            config: 分析配置
        """
        self.config = config
        self.setup_logging()
        
        # 初始化组件
        self.gemini_client = GeminiClient(
            api_key=config.api_key,
            model_name=config.model_name
        )
        self.document_processor = DocumentProcessor()
        self.validator = HAZOPInputValidator()
        self.prompts = HAZOPAnalysisPrompts()
        self.report_prompts = ReportGenerationPrompts()
        
        self.logger.info("HAZOP AI Agent初始化完成")
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('hazop_analysis.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def analyze_tank_system(self, input_file_path: str, reference_logic_path: str, output_path: str) -> Dict:
        """
        分析储罐系统
        
        Args:
            input_file_path: 输入文件路径
            reference_logic_path: 参考逻辑文件路径
            output_path: 输出路径
            
        Returns:
            分析结果字典
        """
        try:
            self.logger.info(f"开始分析储罐系统: {input_file_path}")
            
            # 步骤1: 读取和验证输入文件
            input_content = self.document_processor.read_input_file(input_file_path)
            self.logger.info("输入文件读取成功")
            
            if self.config.enable_validation:
                validation_result = self.validator.validate_input_file(input_content)
                if not validation_result.is_valid:
                    error_msg = f"输入验证失败: {'; '.join(validation_result.errors)}"
                    self.logger.error(error_msg)
                    raise Exception(error_msg)
                self.logger.info("输入验证通过")
            
            # 步骤2: 解析输入数据
            process_data = self.document_processor.parse_input_data(input_content)
            self.logger.info("输入数据解析完成")
            
            # 步骤3: 读取分析逻辑
            analysis_logic = self.document_processor.read_input_file(reference_logic_path)
            self.logger.info("分析逻辑加载完成")
            
            # 步骤4: 执行HAZOP分析
            analysis_results = self._perform_hazop_analysis(
                input_content, analysis_logic, process_data
            )
            self.logger.info("HAZOP分析执行完成")
            
            # 步骤5: 生成报告
            final_report = self._generate_comprehensive_report(
                analysis_results, process_data, input_content
            )
            self.logger.info("分析报告生成完成")
            
            # 步骤6: 保存结果
            self.document_processor.save_analysis_results(final_report, output_path)
            self.logger.info(f"分析结果已保存到: {output_path}")
            
            return {
                'status': 'success',
                'input_file': input_file_path,
                'output_file': output_path,
                'analysis_time': datetime.now().isoformat(),
                'equipment_count': len(process_data.equipment_list),
                'material_count': len(process_data.materials),
                'report_length': len(final_report)
            }
            
        except Exception as e:
            error_msg = f"HAZOP分析失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'status': 'error',
                'error_message': error_msg,
                'input_file': input_file_path
            }
    
    def _perform_hazop_analysis(self, input_content: str, analysis_logic: str, process_data: ProcessData) -> Dict[str, str]:
        """执行HAZOP分析"""
        results = {}
        
        try:
            # 高流量偏差分析
            self.logger.info("开始高流量偏差分析")
            high_flow_prompt = self.prompts.get_high_flow_analysis_prompt(
                input_content, analysis_logic
            )
            high_flow_result = self.gemini_client.generate_hazop_analysis(
                high_flow_prompt, max_retries=self.config.max_retries
            )
            results['high_flow_analysis'] = high_flow_result
            self.logger.info("高流量偏差分析完成")
            
            # 低流量偏差分析
            self.logger.info("开始低流量偏差分析")
            low_flow_prompt = self.prompts.get_low_flow_analysis_prompt(
                input_content, analysis_logic
            )
            low_flow_result = self.gemini_client.generate_hazop_analysis(
                low_flow_prompt, max_retries=self.config.max_retries
            )
            results['low_flow_analysis'] = low_flow_result
            self.logger.info("低流量偏差分析完成")
            
            # 物料危险性分析
            if process_data.materials:
                self.logger.info("开始物料危险性分析")
                material_props = {
                    material.name: {
                        'physical_properties': material.physical_properties,
                        'safety_properties': material.safety_properties,
                        'hazard_classification': material.hazard_classification
                    }
                    for material in process_data.materials
                }
                
                material_hazard_prompt = self.prompts.get_material_hazard_analysis_prompt(material_props)
                material_hazard_result = self.gemini_client.generate_hazop_analysis(
                    material_hazard_prompt, max_retries=self.config.max_retries
                )
                results['material_hazard_analysis'] = material_hazard_result
                self.logger.info("物料危险性分析完成")
            
            # 保护层分析
            self.logger.info("开始保护层分析")
            equipment_info = {
                eq.tag: {
                    'type': eq.type,
                    'specifications': eq.specifications,
                    'operating_conditions': eq.operating_conditions
                }
                for eq in process_data.equipment_list
            }
            
            hazard_summary = results.get('material_hazard_analysis', '物料危险性待评估')
            protection_prompt = self.prompts.get_protection_layer_analysis_prompt(
                equipment_info, hazard_summary
            )
            protection_result = self.gemini_client.generate_hazop_analysis(
                protection_prompt, max_retries=self.config.max_retries
            )
            results['protection_layer_analysis'] = protection_result
            self.logger.info("保护层分析完成")
            
        except Exception as e:
            self.logger.error(f"HAZOP分析过程中出现错误: {str(e)}")
            raise
        
        return results
    
    def _generate_comprehensive_report(self, analysis_results: Dict[str, str], 
                                     process_data: ProcessData, input_content: str) -> str:
        """生成综合报告"""
        try:
            self.logger.info("开始生成综合报告")
            
            # 生成输入数据摘要
            input_summary = self.document_processor.get_summary(process_data)
            
            # 合并所有分析结果
            combined_results = "\n\n".join([
                "## 高流量偏差分析",
                analysis_results.get('high_flow_analysis', ''),
                "## 低流量偏差分析", 
                analysis_results.get('low_flow_analysis', ''),
                "## 物料危险性分析",
                analysis_results.get('material_hazard_analysis', ''),
                "## 保护层分析",
                analysis_results.get('protection_layer_analysis', '')
            ])
            
            # 生成Markdown格式报告
            report_prompt = self.report_prompts.get_markdown_report_prompt(
                combined_results, input_summary
            )
            
            final_report = self.gemini_client.generate_hazop_analysis(
                report_prompt, max_retries=self.config.max_retries
            )
            
            # 生成执行摘要
            executive_summary_prompt = self.report_prompts.get_executive_summary_prompt(
                combined_results
            )
            executive_summary = self.gemini_client.generate_hazop_analysis(
                executive_summary_prompt, max_retries=self.config.max_retries
            )
            
            # 合并最终报告
            complete_report = f"{executive_summary}\n\n{final_report}"
            
            self.logger.info("综合报告生成完成")
            return complete_report
            
        except Exception as e:
            self.logger.error(f"报告生成失败: {str(e)}")
            # 如果报告生成失败，返回原始分析结果
            return "\n\n".join(analysis_results.values())
    
    def batch_analyze(self, input_dir: str, reference_logic_path: str, output_dir: str) -> List[Dict]:
        """
        批量分析
        
        Args:
            input_dir: 输入目录
            reference_logic_path: 参考逻辑文件路径
            output_dir: 输出目录
            
        Returns:
            批量分析结果列表
        """
        results = []
        
        try:
            # 获取所有输入文件
            input_files = [f for f in os.listdir(input_dir) if f.endswith('.txt')]
            
            self.logger.info(f"发现 {len(input_files)} 个输入文件，开始批量分析")
            
            for input_file in input_files:
                input_path = os.path.join(input_dir, input_file)
                output_file = input_file.replace('.txt', '_hazop_report.md')
                output_path = os.path.join(output_dir, output_file)
                
                self.logger.info(f"正在分析: {input_file}")
                
                result = self.analyze_tank_system(
                    input_path, reference_logic_path, output_path
                )
                results.append(result)
                
                if result['status'] == 'success':
                    self.logger.info(f"✓ {input_file} 分析完成")
                else:
                    self.logger.error(f"✗ {input_file} 分析失败: {result.get('error_message', '未知错误')}")
            
            success_count = sum(1 for r in results if r['status'] == 'success')
            self.logger.info(f"批量分析完成: {success_count}/{len(input_files)} 成功")
            
        except Exception as e:
            self.logger.error(f"批量分析失败: {str(e)}")
        
        return results
    
    def get_agent_status(self) -> Dict:
        """获取代理状态"""
        return {
            'gemini_model': self.config.model_name,
            'validation_enabled': self.config.enable_validation,
            'max_retries': self.config.max_retries,
            'output_format': self.config.output_format,
            'api_configured': bool(self.config.api_key)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HAZOP AI分析代理')
    parser.add_argument('--input', '-i', required=True, help='输入文件路径')
    parser.add_argument('--logic', '-l', required=True, help='分析逻辑文件路径')
    parser.add_argument('--output', '-o', required=True, help='输出文件路径')
    parser.add_argument('--api-key', help='Google API密钥')
    parser.add_argument('--model', default='gemini-2.5-pro', help='模型名称')
    parser.add_argument('--batch', action='store_true', help='批量分析模式')
    parser.add_argument('--disable-validation', action='store_true', help='禁用输入验证')
    
    args = parser.parse_args()
    
    # 获取API密钥
    api_key = args.api_key or os.getenv('GOOGLE_API_KEY')
    if not api_key:
        api_key = "AIzaSyCHnEYAzTFT45zDOUUijMX6Cn_Td_Luko8"  # 使用提供的API密钥
    
    # 创建配置
    config = HAZOPAnalysisConfig(
        api_key=api_key,
        model_name=args.model,
        enable_validation=not args.disable_validation
    )
    
    # 创建代理
    agent = HAZOPAgent(config)
    
    try:
        if args.batch:
            # 批量分析
            results = agent.batch_analyze(args.input, args.logic, args.output)
            print(f"批量分析完成，共处理 {len(results)} 个文件")
            
            # 输出结果统计
            success_count = sum(1 for r in results if r['status'] == 'success')
            print(f"成功: {success_count}, 失败: {len(results) - success_count}")
            
        else:
            # 单文件分析
            result = agent.analyze_tank_system(args.input, args.logic, args.output)
            
            if result['status'] == 'success':
                print("✓ HAZOP分析完成")
                print(f"输入文件: {result['input_file']}")
                print(f"输出文件: {result['output_file']}")
                print(f"设备数量: {result['equipment_count']}")
                print(f"物料数量: {result['material_count']}")
                print(f"报告长度: {result['report_length']} 字符")
            else:
                print("✗ HAZOP分析失败")
                print(f"错误信息: {result['error_message']}")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()