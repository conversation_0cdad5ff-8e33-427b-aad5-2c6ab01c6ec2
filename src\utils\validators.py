"""
输入验证工具
用于验证HAZOP分析输入数据的完整性和有效性
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    extracted_data: Dict[str, Any]


class HAZOPInputValidator:
    """HAZOP输入验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 必需的设备类型模式
        self.equipment_patterns = {
            '储罐': r'D\d{4}',
            '调节阀': r'[LFP]V-\d{5}',
            '切断阀': r'XV-\d{5}',
            '泵': r'P\d{4}-\d',
            '控制器': r'[LFP]ICA-\d{5}',
            '压力表': r'PT-\d{5}',
            '液位计': r'LT-\d{5}',
            '流量计': r'FT-\d{5}'
        }
        
        # 物料属性要求
        self.required_material_props = [
            '组成', '温度', '压力', '流量', '密度', '闪点', '沸点'
        ]
        
        # 危险性评估要求
        self.hazard_categories = [
            '火灾爆炸', '毒性', '腐蚀性', '环境危害'
        ]
    
    def validate_input_file(self, file_content: str) -> ValidationResult:
        """
        验证输入文件的完整性
        
        Args:
            file_content: 文件内容
            
        Returns:
            验证结果
        """
        errors = []
        warnings = []
        extracted_data = {}
        
        try:
            # 检查文件编码
            if not self._check_encoding(file_content):
                errors.append("文件编码存在问题，可能包含乱码")
            
            # 提取和验证设备信息
            equipment_data = self._extract_equipment_data(file_content)
            if not equipment_data:
                errors.append("未找到有效的设备信息")
            else:
                extracted_data['equipment'] = equipment_data
                equipment_validation = self._validate_equipment(equipment_data)
                errors.extend(equipment_validation['errors'])
                warnings.extend(equipment_validation['warnings'])
            
            # 提取和验证物料信息
            material_data = self._extract_material_data(file_content)
            if not material_data:
                errors.append("未找到物料属性信息")
            else:
                extracted_data['materials'] = material_data
                material_validation = self._validate_materials(material_data)
                errors.extend(material_validation['errors'])
                warnings.extend(material_validation['warnings'])
            
            # 提取和验证操作条件
            operating_conditions = self._extract_operating_conditions(file_content)
            extracted_data['operating_conditions'] = operating_conditions
            
            # 检查控制系统配置
            control_validation = self._validate_control_systems(file_content)
            errors.extend(control_validation['errors'])
            warnings.extend(control_validation['warnings'])
            
            # 检查安全保护层
            protection_validation = self._validate_protection_layers(file_content)
            warnings.extend(protection_validation['warnings'])
            
        except Exception as e:
            errors.append(f"验证过程中发生错误: {str(e)}")
        
        is_valid = len(errors) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            errors=errors,
            warnings=warnings,
            extracted_data=extracted_data
        )
    
    def _check_encoding(self, content: str) -> bool:
        """检查文件编码是否正确"""
        # 检查是否包含大量乱码字符
        garbled_chars = sum(1 for char in content if ord(char) > 65535)
        total_chars = len(content)
        
        if total_chars == 0:
            return False
        
        garbled_ratio = garbled_chars / total_chars
        return garbled_ratio < 0.1  # 乱码字符不超过10%
    
    def _extract_equipment_data(self, content: str) -> Dict[str, List[str]]:
        """提取设备数据"""
        equipment_data = {}
        
        for equipment_type, pattern in self.equipment_patterns.items():
            matches = re.findall(pattern, content)
            if matches:
                equipment_data[equipment_type] = list(set(matches))  # 去重
        
        return equipment_data
    
    def _extract_material_data(self, content: str) -> Dict[str, Any]:
        """提取物料数据"""
        material_data = {}
        
        # 提取物料名称
        material_patterns = {
            '正丁醇': r'正丁醇|butanol',
            '正丁醛': r'正丁醛|butyraldehyde', 
            '苯酚': r'苯酚|phenol',
            '氮气': r'氮气|nitrogen'
        }
        
        for material, pattern in material_patterns.items():
            if re.search(pattern, content, re.IGNORECASE):
                material_data[material] = self._extract_material_properties(content, material)
        
        return material_data
    
    def _extract_material_properties(self, content: str, material: str) -> Dict[str, str]:
        """提取特定物料的属性"""
        properties = {}
        
        # 提取温度信息
        temp_match = re.search(r'(\d+)\s*[℃°C]', content)
        if temp_match:
            properties['温度'] = temp_match.group(1) + '°C'
        
        # 提取压力信息
        pressure_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:bar|kPa|MPa)', content)
        if pressure_match:
            properties['压力'] = pressure_match.group(0)
        
        # 提取流量信息
        flow_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:t/h|kg/h|m³/h)', content)
        if flow_match:
            properties['流量'] = flow_match.group(0)
        
        return properties
    
    def _extract_operating_conditions(self, content: str) -> Dict[str, str]:
        """提取操作条件"""
        conditions = {}
        
        # 提取温度范围
        temp_range = re.findall(r'(\d+)\s*[℃°C]', content)
        if temp_range:
            conditions['操作温度'] = f"{min(temp_range)}-{max(temp_range)}°C"
        
        # 提取压力范围
        pressure_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(bar|kPa|MPa)', content)
        if pressure_matches:
            conditions['操作压力范围'] = str(pressure_matches)
        
        return conditions
    
    def _validate_equipment(self, equipment_data: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """验证设备配置"""
        errors = []
        warnings = []
        
        # 检查必需设备
        required_equipment = ['储罐', '调节阀', '泵']
        for req_eq in required_equipment:
            if req_eq not in equipment_data:
                errors.append(f"缺少必需设备类型: {req_eq}")
        
        # 检查设备命名规范
        for eq_type, eq_list in equipment_data.items():
            for equipment in eq_list:
                if not self._validate_equipment_naming(equipment, eq_type):
                    warnings.append(f"设备命名可能不规范: {equipment}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_equipment_naming(self, equipment_tag: str, equipment_type: str) -> bool:
        """验证设备命名规范"""
        if equipment_type in self.equipment_patterns:
            pattern = self.equipment_patterns[equipment_type]
            return bool(re.match(pattern, equipment_tag))
        return True
    
    def _validate_materials(self, material_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证物料信息"""
        errors = []
        warnings = []
        
        if not material_data:
            errors.append("未找到物料信息")
            return {'errors': errors, 'warnings': warnings}
        
        for material, properties in material_data.items():
            # 检查关键属性
            missing_props = []
            for prop in ['温度', '压力', '流量']:
                if prop not in properties:
                    missing_props.append(prop)
            
            if missing_props:
                warnings.append(f"物料 {material} 缺少属性: {', '.join(missing_props)}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_control_systems(self, content: str) -> Dict[str, List[str]]:
        """验证控制系统配置"""
        errors = []
        warnings = []
        
        # 检查是否有控制回路配置
        control_indicators = ['LICA', 'FICA', 'PICA', 'TIC']
        found_controls = any(indicator in content for indicator in control_indicators)
        
        if not found_controls:
            warnings.append("未找到明确的控制系统配置")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_protection_layers(self, content: str) -> Dict[str, List[str]]:
        """验证保护层配置"""
        warnings = []
        
        # 检查安全保护层
        protection_elements = ['报警', '联锁', '安全阀', 'PSV']
        found_protections = [elem for elem in protection_elements if elem in content]
        
        if len(found_protections) < 2:
            warnings.append("安全保护层配置可能不足，建议检查报警和联锁系统")
        
        return {'warnings': warnings}
    
    def get_validation_summary(self, result: ValidationResult) -> str:
        """生成验证结果摘要"""
        summary = []
        summary.append(f"验证结果: {'通过' if result.is_valid else '失败'}")
        
        if result.errors:
            summary.append(f"错误数量: {len(result.errors)}")
            for error in result.errors:
                summary.append(f"  - {error}")
        
        if result.warnings:
            summary.append(f"警告数量: {len(result.warnings)}")
            for warning in result.warnings:
                summary.append(f"  - {warning}")
        
        if result.extracted_data:
            summary.append("提取的数据:")
            for key, value in result.extracted_data.items():
                summary.append(f"  - {key}: {len(value) if isinstance(value, (list, dict)) else str(value)}")
        
        return "\n".join(summary)