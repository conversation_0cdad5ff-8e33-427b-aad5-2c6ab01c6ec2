"""
HAZOP分析提示词模板
基于工业安全标准的结构化提示词
"""

class HAZOPAnalysisPrompts:
    """HAZOP分析提示词类"""
    
    @staticmethod
    def get_high_flow_analysis_prompt(input_data: str, analysis_logic: str) -> str:
        """
        获取高流量偏差分析提示词
        
        Args:
            input_data: 输入的储罐分析数据
            analysis_logic: 分析逻辑框架
            
        Returns:
            格式化的提示词
        """
        return f"""
你是一位专业的HAZOP分析专家，需要对储罐系统进行高流量偏差的危险性与可操作性分析。

## 分析任务
请根据以下输入信息和分析逻辑，生成详细的HAZOP分析报告。

## 输入数据
{input_data}

## 分析逻辑框架
{analysis_logic}

## 关键要求
1. **严格遵循4倍流量规则**：高流量偏差故障时，故障流量必须按照正常流量的4倍进行计算
2. **使用实际设备编号**：分析中必须使用输入数据中的实际设备编号（如LICA-10001、LV-10001、D1001等）
3. **定量计算**：提供具体的时间、流量、泄漏量等定量数据
4. **风险评估**：基于物料危险性属性进行全面的风险评估

## 分析结构要求
请按照以下结构进行分析：

### 原因分析
- **进料管线高流量原因**：
  - 分析调节阀故障模式（开大、全开、控制回路故障）
  - 分析切断阀故障模式（卡开、无法关闭）
  - 考虑上游压力异常等因素

- **出料管线高流量原因**：
  - 分析出料调节阀故障模式
  - 分析泵超速运行等因素

### 后果分析
- **定量计算**：
  - 故障流量 = 正常流量 × 4
  - 净流入/流出速率计算
  - 满液/空罐时间估算
  - 泄漏量计算

- **设备影响**：
  - 储罐溢流风险
  - 下游设备影响
  - 泵干转风险

### 风险评估
- **火灾爆炸风险**：
  - 基于物料闪点、爆炸极限评估
  - 分析点火源和爆炸后果
  
- **毒性风险**：
  - 基于LC50、LD50、IDLH值评估
  - 分析毒性蒸气扩散
  
- **热危害风险**：
  - 基于操作温度评估烫伤风险

### 保护层识别
- **液位保护**：高液位报警、高高液位联锁
- **流量保护**：流量高报警、流量联锁
- **压力保护**：安全阀、压力联锁
- **泄漏检测**：可燃气体检测、泄漏报警

## 输出格式
请按照以下markdown格式输出分析结果：

```
- 原因
    - [具体原因描述，使用实际设备编号]
- 后果
    - [详细后果分析，包含定量计算，使用4倍流量规则]
- 有效保护层
    - [保护层名称]
      [保护措施描述]
      [联锁阀门编号]【联锁值】
```

请确保分析结果专业、准确、符合工业安全标准。
"""

    @staticmethod  
    def get_low_flow_analysis_prompt(input_data: str, analysis_logic: str) -> str:
        """
        获取低流量偏差分析提示词
        
        Args:
            input_data: 输入的储罐分析数据
            analysis_logic: 分析逻辑框架
            
        Returns:
            格式化的提示词
        """
        return f"""
你是一位专业的HAZOP分析专家，需要对储罐系统进行低流量偏差的危险性与可操作性分析。

## 分析任务
请根据以下输入信息和分析逻辑，生成详细的HAZOP分析报告。

## 输入数据
{input_data}

## 分析逻辑框架
{analysis_logic}

## 关键要求
1. **使用实际设备编号**：分析中必须使用输入数据中的实际设备编号
2. **定量计算**：提供具体的时间、流量等定量数据
3. **设备保护**：重点关注泵干转保护和下游设备影响
4. **风险评估**：基于物料危险性属性进行风险评估

## 分析结构要求

### 原因分析
- **进料管线低流量原因**：
  - 调节阀故障（关小、全关、控制回路故障）
  - 切断阀故障（误关、卡关）
  - 上游压力不足、管线堵塞等

- **出料管线低流量原因**：
  - 出料调节阀故障
  - 下游背压过高、泵故障等

### 后果分析
- **进料低流量后果**：
  - 储罐液位下降计算
  - 空罐时间估算
  - 下游设备断料影响

- **出料低流量后果**：
  - 储罐液位上升
  - 满液风险评估
  - 溢流可能性分析

### 风险评估
基于泄漏物料危险性进行多维度风险评估

### 保护层识别
- **液位保护**：低液位报警、低低液位联锁
- **流量保护**：流量低报警、流量联锁
- **设备保护**：泵干转保护、压力监测

请确保分析结果专业、准确、符合工业安全标准。
"""

    @staticmethod
    def get_material_hazard_analysis_prompt(material_properties: dict) -> str:
        """
        获取物料危险性分析提示词
        
        Args:
            material_properties: 物料属性字典
            
        Returns:
            物料危险性分析提示词
        """
        return f"""
请对以下物料进行详细的危险性分析：

## 物料属性
{material_properties}

## 分析要求
请从以下角度分析物料危险性：

1. **火灾爆炸危险性**
   - 闪点、自燃温度分析
   - 爆炸极限范围
   - 点火敏感性评估

2. **毒性危险性**
   - 急性毒性参数（LC50、LD50）
   - 慢性毒性影响
   - 职业接触限值（IDLH）

3. **腐蚀性评估**
   - 对设备材料的腐蚀性
   - 对人体的腐蚀危害

4. **环境危害**
   - 生态毒性
   - 生物累积性
   - 环境持久性

5. **应急处理要求**
   - 泄漏应急措施
   - 火灾扑救方法
   - 中毒急救措施

请提供专业、详细的危险性评估结果。
"""

    @staticmethod
    def get_protection_layer_analysis_prompt(equipment_data: dict, hazard_analysis: str) -> str:
        """
        获取保护层分析提示词
        
        Args:
            equipment_data: 设备数据
            hazard_analysis: 危险性分析结果
            
        Returns:
            保护层分析提示词
        """
        return f"""
基于以下设备配置和危险性分析，请设计合适的安全保护层：

## 设备配置
{equipment_data}

## 危险性分析
{hazard_analysis}

## 保护层设计要求
请按照纵深防御原则，设计多层次的安全保护措施：

1. **基本过程控制系统 (BPCS)**
   - 正常控制回路
   - 基本报警功能

2. **安全仪表系统 (SIS)**
   - 安全联锁功能
   - 紧急停车系统

3. **物理保护层**
   - 安全阀、爆破片
   - 防火防爆设施

4. **检测报警系统**
   - 可燃气体检测
   - 有毒气体检测
   - 泄漏检测

5. **应急响应**
   - 应急隔离措施
   - 人员疏散程序

请确保保护层设计符合功能安全标准，并提供具体的设定值和响应逻辑。
"""

    @staticmethod
    def get_report_generation_prompt(analysis_results: str) -> str:
        """
        获取报告生成提示词
        
        Args:
            analysis_results: 分析结果
            
        Returns:
            报告生成提示词
        """
        return f"""
请将以下HAZOP分析结果整理成专业的技术报告：

## 分析结果
{analysis_results}

## 报告格式要求
请按照以下结构组织报告：

1. **执行摘要**
   - 分析目的和范围
   - 主要发现和建议

2. **分析方法**
   - HAZOP分析方法论
   - 分析团队和时间

3. **系统描述**
   - 工艺流程概述
   - 主要设备和控制系统

4. **HAZOP分析结果**
   - 偏差分析详细结果
   - 风险评估结论
   - 建议措施

5. **结论和建议**
   - 主要风险点总结
   - 改进建议
   - 后续行动计划

报告应当专业、清晰、易于理解，符合工业安全技术报告标准。
"""