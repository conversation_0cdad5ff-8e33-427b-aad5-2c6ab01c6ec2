"""
Google Gemini API客户端
用于HAZOP分析的AI模型调用
"""

import os
import logging
from typing import Optional, Dict, Any
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold


class GeminiClient:
    """Google Gemini API客户端类"""
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-2.5-pro"):
        """
        初始化Gemini客户端
        
        Args:
            api_key: Google API密钥，如果为None则从环境变量获取
            model_name: 使用的模型名称
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        self.model_name = model_name
        
        if not self.api_key:
            raise ValueError("需要提供Google API密钥")
        
        # 配置API
        genai.configure(api_key=self.api_key)
        
        # 配置安全设置 - 对于工业安全分析，需要允许危险内容讨论
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
        }
        
        # 初始化模型
        self.model = genai.GenerativeModel(
            model_name=self.model_name,
            safety_settings=self.safety_settings
        )
        
        self.logger = logging.getLogger(__name__)
    
    def generate_hazop_analysis(self, prompt: str, max_retries: int = 3) -> str:
        """
        生成HAZOP分析
        
        Args:
            prompt: 分析提示词
            max_retries: 最大重试次数
            
        Returns:
            生成的HAZOP分析结果
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"开始生成HAZOP分析 (尝试 {attempt + 1}/{max_retries})")
                
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.1,  # 低温度确保分析的一致性
                        top_p=0.8,
                        top_k=40,
                        max_output_tokens=8192,
                    )
                )
                
                if response.text:
                    self.logger.info("HAZOP分析生成成功")
                    return response.text
                else:
                    self.logger.warning(f"第{attempt + 1}次尝试未返回内容")
                    
            except Exception as e:
                self.logger.error(f"第{attempt + 1}次尝试失败: {str(e)}")
                if attempt == max_retries - 1:
                    raise Exception(f"HAZOP分析生成失败，已重试{max_retries}次: {str(e)}")
        
        raise Exception("未能生成HAZOP分析")
    
    def validate_input(self, input_text: str) -> bool:
        """
        验证输入文本的有效性
        
        Args:
            input_text: 输入文本
            
        Returns:
            是否有效
        """
        if not input_text or len(input_text.strip()) < 10:
            return False
        
        # 检查是否包含基本的HAZOP分析要素
        required_elements = ["储罐", "流量", "压力", "温度", "物料"]
        return any(element in input_text for element in required_elements)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        return {
            "model_name": self.model_name,
            "api_key_configured": bool(self.api_key),
            "safety_settings": self.safety_settings
        }