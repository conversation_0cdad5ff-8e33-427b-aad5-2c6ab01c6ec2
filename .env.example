# 环境变量配置文件示例
# 复制此文件为 .env 并填入实际值

# Google Gemini API 配置
GOOGLE_API_KEY=AIzaSyCHnEYAzTFT45zDOUUijMX6Cn_Td_Luko8
GEMINI_MODEL=gemini-2.5-pro

# 应用配置
APP_NAME=HAZOP_AI_Agent
APP_VERSION=1.0.0
DEBUG=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=hazop_analysis.log

# 分析配置
MAX_RETRIES=3
TEMPERATURE=0.1
ENABLE_VALIDATION=true
OUTPUT_FORMAT=markdown

# 文件路径配置
INPUT_DIR=input
OUTPUT_DIR=output
REFERENCE_DIR=refrences

# 安全配置
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=txt,md,json

# 性能配置
BATCH_SIZE=5
TIMEOUT=300  # 5 minutes