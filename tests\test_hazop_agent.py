"""
HAZOP Agent 测试用例
"""

import os
import sys
import pytest
import tempfile
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.hazop_agent import H<PERSON><PERSON><PERSON><PERSON>gent, HAZOPAnalysisConfig
from src.utils.validators import HAZOPInputValidator, ValidationResult
from src.document_processor import DocumentProcessor


class TestHAZOPAgent:
    """HAZOP Agent测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.config = HAZOPAnalysisConfig(
            api_key="test_api_key",
            model_name="gemini-2.5-pro",
            enable_validation=False  # 测试时禁用验证避免网络调用
        )
    
    def test_agent_initialization(self):
        """测试代理初始化"""
        with patch('src.hazop_agent.GeminiClient') as mock_client:
            agent = HAZOPAgent(self.config)
            assert agent.config.api_key == "test_api_key"
            assert agent.config.model_name == "gemini-2.5-pro"
            mock_client.assert_called_once()
    
    def test_get_agent_status(self):
        """测试获取代理状态"""
        with patch('src.hazop_agent.GeminiClient'):
            agent = HAZOPAgent(self.config)
            status = agent.get_agent_status()
            
            assert status['gemini_model'] == "gemini-2.5-pro"
            assert status['validation_enabled'] == False
            assert status['api_configured'] == True
    
    @patch('src.hazop_agent.GeminiClient')
    def test_analyze_tank_system_success(self, mock_client_class):
        """测试储罐系统分析成功场景"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            test_input = """
            储罐分析测试数据
            D1001: 储罐
            LICA-10001: 液位控制器
            LV-10001: 液位调节阀
            正丁醇: 95wt%
            温度: 40°C
            压力: 1 barG
            流量: 5 t/h
            """
            f.write(test_input)
            input_file = f.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            logic_content = """
            高流量HAZOP分析逻辑
            - 故障流量 = 正常流量 × 4
            - 风险评估方法
            """
            f.write(logic_content)
            logic_file = f.name
        
        output_file = tempfile.mktemp(suffix='.md')
        
        try:
            # 模拟Gemini客户端
            mock_client = Mock()
            mock_client.generate_hazop_analysis.return_value = "测试分析结果"
            mock_client_class.return_value = mock_client
            
            agent = HAZOPAgent(self.config)
            result = agent.analyze_tank_system(input_file, logic_file, output_file)
            
            assert result['status'] == 'success'
            assert 'input_file' in result
            assert 'output_file' in result
            assert 'analysis_time' in result
            
        finally:
            # 清理临时文件
            for file_path in [input_file, logic_file, output_file]:
                if os.path.exists(file_path):
                    os.unlink(file_path)
    
    @patch('src.hazop_agent.GeminiClient')
    def test_analyze_tank_system_file_not_found(self, mock_client_class):
        """测试文件不存在的错误处理"""
        agent = HAZOPAgent(self.config)
        result = agent.analyze_tank_system(
            "nonexistent_input.txt",
            "nonexistent_logic.txt", 
            "output.md"
        )
        
        assert result['status'] == 'error'
        assert 'error_message' in result
        assert '文件不存在' in result['error_message'] or 'HAZOP分析失败' in result['error_message']


class TestHAZOPInputValidator:
    """输入验证器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.validator = HAZOPInputValidator()
    
    def test_validate_valid_input(self):
        """测试有效输入验证"""
        test_content = """
        储罐分析数据
        D1001: 储罐设备
        LICA-10001: 液位控制器
        LV-10001: 液位调节阀
        正丁醇组成: 95wt%
        操作温度: 40°C
        操作压力: 1 barG
        正常流量: 5 t/h
        联锁系统已配置
        报警功能正常
        """
        
        result = self.validator.validate_input_file(test_content)
        
        # 基本验证应该通过（即使有警告）
        assert isinstance(result, ValidationResult)
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_validate_empty_input(self):
        """测试空输入验证"""
        result = self.validator.validate_input_file("")
        
        assert not result.is_valid
        assert len(result.errors) > 0
    
    def test_validate_garbled_input(self):
        """测试乱码输入验证"""
        # 创建包含大量乱码字符的内容
        garbled_content = "储罐" + "".join([chr(i) for i in range(65536, 65600)])
        
        result = self.validator.validate_input_file(garbled_content)
        
        # 应该检测到编码问题
        assert not result.is_valid or len(result.warnings) > 0


class TestDocumentProcessor:
    """文档处理器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.processor = DocumentProcessor()
    
    def test_read_input_file_utf8(self):
        """测试UTF-8文件读取"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            test_content = "储罐分析测试数据\n包含中文内容"
            f.write(test_content)
            file_path = f.name
        
        try:
            content = self.processor.read_input_file(file_path)
            assert content == test_content
        finally:
            os.unlink(file_path)
    
    def test_parse_input_data(self):
        """测试输入数据解析"""
        test_content = """
        储罐系统HAZOP分析
        设备清单:
        D1001: 储罐，容积：10m³，设计压力：5 barG
        LICA-10001: 液位控制器，测量范围：0-100%
        LV-10001: 液位调节阀，口径：DN50
        
        物料信息:
        正丁醇: CAS 123-72-8, 闪点：-6.7°C, 沸点：75°C
        操作条件: 温度40°C, 压力1 barG, 流量5 t/h
        """
        
        process_data = self.processor.parse_input_data(test_content)
        
        assert len(process_data.equipment_list) > 0
        assert len(process_data.materials) > 0
        assert process_data.process_description
        
        # 检查是否正确识别了设备
        equipment_tags = [eq.tag for eq in process_data.equipment_list]
        assert any('D1001' in tag for tag in equipment_tags)
        assert any('LICA-10001' in tag for tag in equipment_tags)
    
    def test_get_summary(self):
        """测试数据摘要生成"""
        from src.document_processor import ProcessData, EquipmentInfo, MaterialInfo
        
        # 创建测试数据
        equipment = [
            EquipmentInfo("D1001", "储罐", "储罐", {}, {}),
            EquipmentInfo("LV-10001", "调节阀", "调节阀", {}, {})
        ]
        
        materials = [
            MaterialInfo("正丁醇", "123-72-8", {}, {}, {}, [])
        ]
        
        process_data = ProcessData(
            equipment_list=equipment,
            materials=materials,
            operating_conditions={},
            control_systems={},
            safety_systems={},
            process_description="测试工艺"
        )
        
        summary = self.processor.get_summary(process_data)
        
        assert "设备数量: 2" in summary
        assert "物料种类: 1" in summary
        assert "测试工艺" in summary


def test_config_loading():
    """测试配置加载"""
    from config import get_config, validate_config
    
    config = get_config()
    assert config.name == "HAZOP_AI_Agent"
    assert config.api.gemini_model == "gemini-2.5-pro"
    
    errors = validate_config(config)
    # 在测试环境中，可能没有配置真实的API密钥，所以允许有配置错误
    assert isinstance(errors, list)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])