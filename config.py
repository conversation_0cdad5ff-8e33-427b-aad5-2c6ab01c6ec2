"""
配置管理模块
"""

import os
from typing import List, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


@dataclass
class APIConfig:
    """API配置"""
    google_api_key: str
    gemini_model: str = "gemini-2.5-pro"
    max_retries: int = 3
    timeout: int = 300  # 5分钟
    

@dataclass
class AnalysisConfig:
    """分析配置"""
    temperature: float = 0.1
    enable_validation: bool = True
    output_format: str = "markdown"  # markdown, json, html
    batch_size: int = 5


@dataclass
class FileConfig:
    """文件配置"""
    input_dir: str = "input"
    output_dir: str = "output"
    reference_dir: str = "refrences"
    max_file_size: int = 10485760  # 10MB
    allowed_file_types: List[str] = None
    
    def __post_init__(self):
        if self.allowed_file_types is None:
            self.allowed_file_types = ["txt", "md", "json"]


@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    file: str = "hazop_analysis.log"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


@dataclass
class AppConfig:
    """应用总配置"""
    name: str = "HAZOP_AI_Agent"
    version: str = "1.0.0"
    debug: bool = False
    api: APIConfig = None
    analysis: AnalysisConfig = None
    file: FileConfig = None
    log: LogConfig = None
    
    def __post_init__(self):
        if self.api is None:
            self.api = APIConfig(
                google_api_key=os.getenv("GOOGLE_API_KEY", "AIzaSyCHnEYAzTFT45zDOUUijMX6Cn_Td_Luko8"),
                gemini_model=os.getenv("GEMINI_MODEL", "gemini-2.5-pro"),
                max_retries=int(os.getenv("MAX_RETRIES", "3")),
                timeout=int(os.getenv("TIMEOUT", "300"))
            )
        
        if self.analysis is None:
            self.analysis = AnalysisConfig(
                temperature=float(os.getenv("TEMPERATURE", "0.1")),
                enable_validation=os.getenv("ENABLE_VALIDATION", "true").lower() == "true",
                output_format=os.getenv("OUTPUT_FORMAT", "markdown"),
                batch_size=int(os.getenv("BATCH_SIZE", "5"))
            )
        
        if self.file is None:
            self.file = FileConfig(
                input_dir=os.getenv("INPUT_DIR", "input"),
                output_dir=os.getenv("OUTPUT_DIR", "output"),
                reference_dir=os.getenv("REFERENCE_DIR", "refrences"),
                max_file_size=int(os.getenv("MAX_FILE_SIZE", "10485760")),
                allowed_file_types=os.getenv("ALLOWED_FILE_TYPES", "txt,md,json").split(",")
            )
        
        if self.log is None:
            self.log = LogConfig(
                level=os.getenv("LOG_LEVEL", "INFO"),
                file=os.getenv("LOG_FILE", "hazop_analysis.log")
            )
        
        self.debug = os.getenv("DEBUG", "false").lower() == "true"


def get_config() -> AppConfig:
    """获取应用配置"""
    return AppConfig()


def validate_config(config: AppConfig) -> List[str]:
    """
    验证配置有效性
    
    Args:
        config: 应用配置
        
    Returns:
        错误信息列表
    """
    errors = []
    
    # 验证API配置
    if not config.api.google_api_key:
        errors.append("Google API密钥未配置")
    
    if not config.api.gemini_model:
        errors.append("Gemini模型名称未配置")
    
    if config.api.max_retries < 1:
        errors.append("最大重试次数必须大于0")
    
    if config.api.timeout < 30:
        errors.append("超时时间不能少于30秒")
    
    # 验证分析配置
    if not 0 <= config.analysis.temperature <= 1:
        errors.append("温度参数必须在0-1之间")
    
    if config.analysis.output_format not in ["markdown", "json", "html"]:
        errors.append("输出格式必须是markdown、json或html之一")
    
    if config.analysis.batch_size < 1:
        errors.append("批量大小必须大于0")
    
    # 验证文件配置
    if config.file.max_file_size < 1024:  # 最小1KB
        errors.append("最大文件大小不能小于1KB")
    
    # 验证日志配置
    valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    if config.log.level.upper() not in valid_log_levels:
        errors.append(f"日志级别必须是{valid_log_levels}之一")
    
    return errors


def create_directories(config: AppConfig) -> None:
    """
    创建必要的目录
    
    Args:
        config: 应用配置
    """
    directories = [
        config.file.input_dir,
        config.file.output_dir,
        config.file.reference_dir,
        "logs",
        "temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# 全局配置实例
CONFIG = get_config()

# 验证配置
config_errors = validate_config(CONFIG)
if config_errors:
    raise ValueError(f"配置错误: {'; '.join(config_errors)}")

# 创建目录
create_directories(CONFIG)