"""
HAZOP AI Agent 演示程序
不依赖外部API的功能演示
"""

import os
import sys
from datetime import datetime

# 添加项目路径
sys.path.append('.')

def demo_document_processing():
    """演示文档处理功能"""
    print("📄 文档处理演示")
    print("-" * 40)
    
    # 创建测试内容
    test_content = """
    储罐系统HAZOP分析
    项目编号: 1001-B-101-001
    
    设备清单:
    D1001: 储罐，容积10m³，设计压力5barG，操作温度40°C
    LICA-10001: 液位控制器
    LV-10001: 液位调节阀
    XV-10004: 切断阀  
    P1001-1: 离心泵
    
    物料信息:
    正丁醇: 95wt%, CAS: 123-72-8
    正丁醛: 5wt%, CAS: 71-36-3
    操作温度: 40°C
    操作压力: 1 barG
    正常流量: 5 t/h
    
    控制系统:
    液位控制回路: LICA-10001控制LV-10001
    流量控制回路: FICA-10001控制FV-10001
    
    安全系统:
    液位报警: LIAS-10003高报警80%，高高联锁90%
    安全阀: PSV-10001，设定压力5barG
    """
    
    try:
        # 导入文档处理器（仅导入，不依赖外部库）
        from src.document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        
        # 解析数据
        print("🔍 解析输入数据...")
        process_data = processor.parse_input_data(test_content)
        
        print(f"   📊 识别设备: {len(process_data.equipment_list)} 台")
        for eq in process_data.equipment_list[:3]:  # 显示前3个
            print(f"      - {eq.tag}: {eq.type}")
        
        print(f"   🧪 识别物料: {len(process_data.materials)} 种")
        for mat in process_data.materials:
            print(f"      - {mat.name}: {mat.cas_number}")
        
        print(f"   ⚙️  控制系统: {len(process_data.control_systems)} 个")
        for sys_name, sys_info in process_data.control_systems.items():
            print(f"      - {sys_name}: {sys_info}")
        
        print(f"   🛡️  安全系统: {len(process_data.safety_systems)} 个")
        
        # 生成摘要
        summary = processor.get_summary(process_data)
        print(f"\n📋 数据摘要:")
        print(f"   {summary}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 处理失败: {str(e)}")
        return False

def demo_input_validation():
    """演示输入验证功能"""
    print("\n🔍 输入验证演示")
    print("-" * 40)
    
    try:
        from src.utils.validators import HAZOPInputValidator
        
        validator = HAZOPInputValidator()
        
        # 测试有效输入
        valid_content = """
        储罐系统HAZOP分析
        D1001: 储罐
        LICA-10001: 液位控制器
        正丁醇: 40°C, 1barG, 5t/h
        联锁系统正常
        """
        
        print("✅ 测试有效输入...")
        result = validator.validate_input_file(valid_content)
        print(f"   验证结果: {'通过' if result.is_valid else '失败'}")
        
        if result.warnings:
            print(f"   警告: {len(result.warnings)} 个")
            for warning in result.warnings[:2]:
                print(f"      - {warning}")
        
        # 测试无效输入
        invalid_content = "无效的输入内容"
        
        print("\n❌ 测试无效输入...")
        result = validator.validate_input_file(invalid_content)
        print(f"   验证结果: {'通过' if result.is_valid else '失败'}")
        
        if result.errors:
            print(f"   错误: {len(result.errors)} 个")
            for error in result.errors[:2]:
                print(f"      - {error}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 验证失败: {str(e)}")
        return False

def demo_prompt_generation():
    """演示提示词生成功能"""
    print("\n💬 提示词生成演示")
    print("-" * 40)
    
    try:
        from src.prompts.hazop_analysis import HAZOPAnalysisPrompts
        
        prompts = HAZOPAnalysisPrompts()
        
        # 示例输入数据
        sample_input = "D1001储罐，LICA-10001控制器，正丁醇40°C，5t/h"
        sample_logic = "高流量分析：故障流量=正常流量×4"
        
        print("🔧 生成高流量分析提示词...")
        high_flow_prompt = prompts.get_high_flow_analysis_prompt(sample_input, sample_logic)
        
        prompt_preview = high_flow_prompt[:200] + "..." if len(high_flow_prompt) > 200 else high_flow_prompt
        print(f"   📝 提示词预览: {prompt_preview}")
        print(f"   📏 提示词长度: {len(high_flow_prompt)} 字符")
        
        print("\n🔧 生成低流量分析提示词...")
        low_flow_prompt = prompts.get_low_flow_analysis_prompt(sample_input, sample_logic)
        print(f"   📏 提示词长度: {len(low_flow_prompt)} 字符")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 生成失败: {str(e)}")
        return False

def demo_report_generation():
    """演示报告生成功能"""
    print("\n📊 报告生成演示")
    print("-" * 40)
    
    try:
        from src.prompts.report_generation import ReportGenerationPrompts
        
        report_prompts = ReportGenerationPrompts()
        
        # 模拟分析结果
        mock_results = """
        高流量偏差分析:
        - 原因: LICA-10001故障，LV-10001开大
        - 后果: 故障流量20t/h，满液30分钟，泄漏7.5t
        - 保护层: LIAS-10003高高联锁90%
        
        风险评估: 火灾爆炸风险高
        """
        
        mock_summary = "设备5台，物料2种，储罐系统分析"
        
        print("📝 生成Markdown报告提示词...")
        report_prompt = report_prompts.get_markdown_report_prompt(mock_results, mock_summary)
        
        prompt_preview = report_prompt[:300] + "..." if len(report_prompt) > 300 else report_prompt
        print(f"   📝 提示词预览: {prompt_preview}")
        print(f"   📏 提示词长度: {len(report_prompt)} 字符")
        
        print("\n📋 生成执行摘要提示词...")
        summary_prompt = report_prompts.get_executive_summary_prompt(mock_results)
        print(f"   📏 提示词长度: {len(summary_prompt)} 字符")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 生成失败: {str(e)}")
        return False

def demo_config_management():
    """演示配置管理功能"""
    print("\n⚙️  配置管理演示")
    print("-" * 40)
    
    try:
        from config import CONFIG, validate_config
        
        print(f"📋 应用名称: {CONFIG.name}")
        print(f"📋 版本: {CONFIG.version}")
        print(f"🤖 AI模型: {CONFIG.api.gemini_model}")
        print(f"🔑 API配置: {'已配置' if CONFIG.api.google_api_key else '未配置'}")
        print(f"🔍 启用验证: {CONFIG.analysis.enable_validation}")
        print(f"📄 输出格式: {CONFIG.analysis.output_format}")
        print(f"🔄 最大重试: {CONFIG.api.max_retries}")
        
        # 验证配置
        print("\n🔍 配置验证...")
        errors = validate_config(CONFIG)
        if errors:
            print(f"   ⚠️  配置警告: {len(errors)} 个")
            for error in errors[:2]:
                print(f"      - {error}")
        else:
            print("   ✅ 配置验证通过")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 配置失败: {str(e)}")
        return False

def main():
    """主演示函数"""
    print("🚀 HAZOP AI Agent 功能演示")
    print("=" * 60)
    print(f"📅 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    print()
    
    # 执行各项演示
    demos = [
        ("文档处理", demo_document_processing),
        ("输入验证", demo_input_validation), 
        ("提示词生成", demo_prompt_generation),
        ("报告生成", demo_report_generation),
        ("配置管理", demo_config_management)
    ]
    
    results = []
    for name, demo_func in demos:
        try:
            success = demo_func()
            results.append((name, success))
        except Exception as e:
            print(f"   ❌ {name}演示失败: {str(e)}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 演示结果总结:")
    
    success_count = 0
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n🎯 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 所有功能演示成功！项目已准备就绪。")
        print("\n📋 后续步骤:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 配置API: 编辑 .env 文件")
        print("3. 运行完整测试: python src/hazop_agent.py --help")
    else:
        print("\n⚠️  部分功能需要进一步检查，但核心架构正常。")
    
    return success_count == len(results)

if __name__ == "__main__":
    main()