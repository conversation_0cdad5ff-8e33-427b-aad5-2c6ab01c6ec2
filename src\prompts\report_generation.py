"""
HAZOP报告生成提示词模板
用于生成标准化的HAZOP分析报告
"""

class ReportGenerationPrompts:
    """HAZOP报告生成提示词类"""
    
    @staticmethod
    def get_markdown_report_prompt(hazop_results: str, input_summary: str) -> str:
        """
        获取Markdown格式报告生成提示词
        
        Args:
            hazop_results: HAZOP分析结果
            input_summary: 输入数据摘要
            
        Returns:
            Markdown报告生成提示词
        """
        return f"""
请将以下HAZOP分析结果生成符合工业标准的技术报告，使用Markdown格式输出。

## 输入数据摘要
{input_summary}

## HAZOP分析结果
{hazop_results}

## 报告格式要求
请严格按照以下Markdown结构生成报告：

```markdown
# 储罐系统HAZOP分析报告

## 1. 项目概述
### 1.1 项目名称
### 1.2 分析范围
### 1.3 分析日期
### 1.4 分析方法

## 2. 系统描述
### 2.1 工艺流程
### 2.2 主要设备
### 2.3 操作条件
### 2.4 物料特性

## 3. HAZOP分析结果
### 3.1 高流量偏差分析
#### 3.1.1 原因分析
#### 3.1.2 后果分析
#### 3.1.3 现有保护层
#### 3.1.4 风险评估
#### 3.1.5 建议措施

### 3.2 低流量偏差分析
#### 3.2.1 原因分析
#### 3.2.2 后果分析
#### 3.2.3 现有保护层
#### 3.2.4 风险评估
#### 3.2.5 建议措施

## 4. 风险评估总结
### 4.1 主要风险点
### 4.2 风险等级评定
### 4.3 关键防护措施

## 5. 结论与建议
### 5.1 分析结论
### 5.2 安全改进建议
### 5.3 后续行动计划

## 6. 附录
### 6.1 设备清单
### 6.2 物料安全数据
### 6.3 参考标准
```

## 内容要求
1. **专业性**：使用准确的工业安全术语
2. **完整性**：包含所有必要的分析要素
3. **可读性**：结构清晰，逻辑严密
4. **实用性**：提供具体可操作的建议

## 格式要求
1. 使用标准Markdown语法
2. 表格数据使用Markdown表格格式
3. 重要信息使用**粗体**强调
4. 代码或设备编号使用`反引号`
5. 数值计算使用合适的单位

请确保报告内容准确、专业、符合工业安全分析标准。
"""

    @staticmethod
    def get_executive_summary_prompt(analysis_data: str) -> str:
        """
        获取执行摘要生成提示词
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            执行摘要提示词
        """
        return f"""
请基于以下HAZOP分析数据生成简洁的执行摘要：

## 分析数据
{analysis_data}

## 执行摘要要求
请生成一个不超过500字的执行摘要，包含：

1. **分析目的**：简述HAZOP分析的目标和范围
2. **主要发现**：总结识别的关键风险点（不超过3个）
3. **风险等级**：整体风险评估结论
4. **关键建议**：最重要的安全改进措施（不超过3条）
5. **紧急程度**：需要立即关注的问题

## 输出格式
```markdown
## 执行摘要

### 分析目的
[简述分析目标]

### 主要发现
1. [关键风险点1]
2. [关键风险点2] 
3. [关键风险点3]

### 风险等级
[整体风险评估]

### 关键建议
1. [重要建议1]
2. [重要建议2]
3. [重要建议3]

### 紧急程度
[需要立即关注的问题]
```

请确保摘要内容准确、简洁、突出重点。
"""

    @staticmethod
    def get_risk_matrix_prompt(risk_assessment: str) -> str:
        """
        获取风险矩阵生成提示词
        
        Args:
            risk_assessment: 风险评估结果
            
        Returns:
            风险矩阵生成提示词
        """
        return f"""
请基于以下风险评估结果生成风险矩阵表：

## 风险评估数据
{risk_assessment}

## 风险矩阵要求
请按照以下格式生成风险矩阵：

### 风险等级定义
- **高风险 (H)**：需要立即采取措施
- **中风险 (M)**：需要计划改进措施
- **低风险 (L)**：可接受的风险水平

### 评估维度
- **可能性**：极不可能(1) | 不太可能(2) | 可能(3) | 很可能(4) | 几乎确定(5)
- **严重程度**：轻微(1) | 一般(2) | 严重(3) | 重大(4) | 灾难性(5)

### 风险矩阵表格
请生成Markdown表格格式的风险矩阵：

```markdown
| 风险场景 | 可能性 | 严重程度 | 风险等级 | 现有控制措施 | 建议措施 |
|---------|--------|----------|----------|-------------|----------|
| [场景1] | [1-5] | [1-5] | [H/M/L] | [控制措施] | [建议] |
| [场景2] | [1-5] | [1-5] | [H/M/L] | [控制措施] | [建议] |
```

请确保风险评估科学合理，符合工业安全标准。
"""

    @staticmethod
    def get_action_plan_prompt(recommendations: str) -> str:
        """
        获取行动计划生成提示词
        
        Args:
            recommendations: 建议措施
            
        Returns:
            行动计划提示词
        """
        return f"""
请基于以下建议措施制定详细的行动计划：

## 建议措施
{recommendations}

## 行动计划要求
请生成包含以下要素的行动计划：

### 计划格式
```markdown
## 安全改进行动计划

| 序号 | 改进措施 | 优先级 | 负责部门 | 预计完成时间 | 预算估算 | 成功标准 |
|------|----------|--------|----------|------------|----------|----------|
| 1 | [具体措施] | [高/中/低] | [部门] | [时间] | [预算] | [标准] |
| 2 | [具体措施] | [高/中/低] | [部门] | [时间] | [预算] | [标准] |

### 实施阶段
#### 第一阶段（紧急措施）- 1个月内
- [紧急措施1]
- [紧急措施2]

#### 第二阶段（重要改进）- 3个月内  
- [重要改进1]
- [重要改进2]

#### 第三阶段（系统优化）- 6个月内
- [系统优化1]
- [系统优化2]

### 监控与评估
- **定期检查**：[检查频率和内容]
- **效果评估**：[评估方法和指标]
- **持续改进**：[改进机制]
```

## 优先级划分标准
- **高优先级**：涉及人员安全，需要立即实施
- **中优先级**：涉及环境或财产安全，需要尽快实施
- **低优先级**：改善操作条件，可以计划实施

请确保行动计划具体可行，时间安排合理。
"""

    @staticmethod
    def get_appendix_prompt(technical_data: str) -> str:
        """
        获取附录生成提示词
        
        Args:
            technical_data: 技术数据
            
        Returns:
            附录生成提示词
        """
        return f"""
请基于以下技术数据生成报告附录：

## 技术数据
{technical_data}

## 附录要求
请生成包含以下内容的附录：

```markdown
## 附录

### 附录A：设备清单
| 设备编号 | 设备名称 | 规格型号 | 操作参数 | 安全等级 |
|----------|----------|----------|----------|----------|
| [编号] | [名称] | [规格] | [参数] | [等级] |

### 附录B：物料安全数据表 (MSDS)
#### B.1 物料基本信息
- 化学名称：
- CAS号：
- 分子式：
- 物理状态：

#### B.2 危险性标识
- 危险性类别：
- 警示词：
- 危险性说明：
- 防范说明：

#### B.3 理化性质
- 闪点：
- 自燃温度：
- 爆炸极限：
- 密度：
- 沸点：

### 附录C：参考标准
- [标准1]
- [标准2]
- [标准3]

### 附录D：计算公式
#### D.1 流量计算
- 故障流量 = 正常流量 × 4
- 净流入速率 = 进料流量 - 出料流量

#### D.2 时间计算
- 满液时间 = 剩余容积 / 净流入速率
- 空罐时间 = 当前液位容积 / 净流出速率

#### D.3 泄漏量计算
- 泄漏量 = 泄漏速率 × 泄漏时间
```

请确保附录信息准确、完整、实用。
"""