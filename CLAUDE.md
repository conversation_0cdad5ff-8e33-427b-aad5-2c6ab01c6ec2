# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a HAZOP (Hazard and Operability Study) AI Agent project built with LangChain framework to automate industrial safety risk analysis for tank storage systems. The AI agent analyzes tank flow deviation scenarios and generates comprehensive HAZOP reports using Google Gemini AI.

## Architecture

### Core Components
- **LangChain Framework**: Primary framework for building the AI agent workflow
- **Google Gemini Integration**: Uses `gemini-2.5-pro` model for HAZOP analysis
- **Document Processing**: Handles input files (tank analysis specifications) and generates structured reports
- **Prompt Engineering**: Specialized prompts for HAZOP analysis following industry standards

### Key Features
- Automated HAZOP analysis for tank storage systems
- Flow deviation analysis (high flow, low flow scenarios)
- Risk assessment including fire/explosion, toxicity, and thermal hazards
- Protection layer identification and evaluation
- Quantitative calculations for flow rates, timing, and material release scenarios

## Development Setup

### Dependencies
```bash
pip install langchain
pip install google-generativeai
pip install python-dotenv
```

### Environment Variables
```bash
GOOGLE_API_KEY=AIzaSyCHnEYAzTFT45zDOUUijMX6Cn_Td_Luko8
GEMINI_MODEL=gemini-2.5-pro
```

### Project Structure
```
HAZOP_AI/
├── src/
│   ├── hazop_agent.py          # Main HAZOP AI agent
│   ├── document_processor.py   # Input/output document handling
│   ├── prompts/
│   │   ├── hazop_analysis.py   # HAZOP analysis prompts
│   │   └── report_generation.py # Report generation prompts
│   └── utils/
│       ├── gemini_client.py    # Google Gemini API client
│       └── validators.py       # Input validation utilities
├── refrences/                  # Reference documents and analysis logic
├── input/                      # Input tank analysis files
├── output/                     # Generated HAZOP reports
└── tests/                      # Unit tests
```

## Development Commands

### Running the HAZOP Agent
```bash
python src/hazop_agent.py --input "refrences/储罐分析 原方案.txt" --output "output/hazop_report.md"
```

### Testing
```bash
python -m pytest tests/
python -m pytest tests/test_hazop_agent.py -v
```

### Linting and Formatting
```bash
flake8 src/
black src/
isort src/
```

## Key Implementation Requirements

### HAZOP Analysis Logic
- Must follow the analysis framework defined in `储罐高流量HAZOP分析逻辑.md`
- **Critical Rule**: High flow deviation scenarios must use 4x normal flow rate for fault calculations
- Equipment identifiers must match input file specifications (e.g., LICA-10001, LV-10001, D1001)
- Analysis must include:
  - Cause identification (valve failures, control system failures)
  - Quantitative consequence analysis (flow rates, timing, leak quantities)
  - Risk assessment (fire/explosion, toxicity, thermal hazards)
  - Protection layer evaluation (alarms, interlocks, emergency systems)

### Input Processing
- Parse structured Chinese-language tank specification files
- Extract equipment parameters, material properties, operating conditions
- Validate completeness of input data before analysis

### Output Generation
- Generate reports in markdown format matching the structure of existing examples
- Include quantitative calculations with specific values
- Provide detailed cause-consequence relationships
- List effective protection layers with setpoints

### Prompt Engineering Guidelines
- Use structured prompts that incorporate HAZOP methodology
- Include specific calculation requirements (4x flow rate rule)
- Reference material hazard properties for risk assessment
- Ensure consistent equipment naming conventions

## Material Properties Handling

The system handles various chemical substances with their safety-critical properties:
- **Butanol/Butyraldehyde mixtures**: Flammable, moderate toxicity
- **Phenol**: Toxic, corrosive, moderate flammability
- **Nitrogen**: Inert gas, asphyxiation hazard

Each material requires specific risk assessment approaches based on:
- Flash point and flammability limits
- Toxicity parameters (LC50, LD50, IDLH values)
- Operating temperature and thermal hazards
- Environmental impact considerations

## Quality Assurance

### Validation Requirements
- Cross-reference generated reports with reference examples
- Verify quantitative calculations are accurate and consistent
- Ensure all equipment identifiers match input specifications
- Validate that protection layer recommendations are appropriate

### Error Handling
- Graceful handling of malformed input files
- API rate limiting and retry logic for Gemini calls
- Comprehensive logging for debugging analysis issues
- Input validation with meaningful error messages