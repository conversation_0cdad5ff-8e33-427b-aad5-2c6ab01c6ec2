"""
测试项目设置
检查项目结构和基本配置
"""

import os
import sys
from pathlib import Path

def check_project_structure():
    """检查项目结构"""
    print("🔍 检查项目结构...")
    
    required_dirs = [
        'src',
        'src/prompts', 
        'src/utils',
        'tests',
        'input',
        'output',
        'refrences'
    ]
    
    required_files = [
        'src/hazop_agent.py',
        'src/document_processor.py',
        'src/utils/gemini_client.py',
        'src/utils/validators.py',
        'src/prompts/hazop_analysis.py',
        'src/prompts/report_generation.py',
        'config.py',
        'requirements.txt',
        'README.md',
        'example_usage.py'
    ]
    
    # 检查目录
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"   ✅ {dir_path}/")
    
    if missing_dirs:
        print(f"   ❌ 缺少目录: {', '.join(missing_dirs)}")
    
    # 检查文件
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"   ✅ {file_path}")
    
    if missing_files:
        print(f"   ❌ 缺少文件: {', '.join(missing_files)}")
    
    return len(missing_dirs) == 0 and len(missing_files) == 0

def check_reference_files():
    """检查参考文件"""
    print("\n📚 检查参考文件...")
    
    ref_files = [
        'refrences/储罐分析 原方案.txt',
        'refrences/储罐高流量HAZOP分析逻辑.md',
        'refrences/储罐高流量HAZOP分析报告结果.md',
        'refrences/储罐低流量HAZOP分析逻辑.md'
    ]
    
    for file_path in ref_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path}")

def check_config():
    """检查配置文件"""
    print("\n⚙️  检查配置...")
    
    try:
        # 基本导入测试
        sys.path.append('.')
        
        # 检查配置文件
        if os.path.exists('config.py'):
            print("   ✅ config.py 存在")
            
            # 尝试导入配置（不依赖外部库）
            config_content = open('config.py', 'r', encoding='utf-8').read()
            if 'class AppConfig' in config_content:
                print("   ✅ 配置类定义正确")
            
        # 检查环境变量文件
        if os.path.exists('.env.example'):
            print("   ✅ .env.example 存在")
        
        # 检查requirements.txt
        if os.path.exists('requirements.txt'):
            with open('requirements.txt', 'r') as f:
                requirements = f.read()
                if 'google-generativeai' in requirements:
                    print("   ✅ Google Generative AI 依赖已配置")
                if 'langchain' in requirements:
                    print("   ✅ LangChain 依赖已配置")
                    
    except Exception as e:
        print(f"   ❌ 配置检查失败: {str(e)}")

def check_file_encoding():
    """检查文件编码"""
    print("\n🔤 检查参考文件编码...")
    
    test_file = 'refrences/储罐分析 原方案.txt'
    if os.path.exists(test_file):
        try:
            # 尝试不同编码方式读取
            encodings = ['utf-8', 'gbk', 'gb2312']
            for encoding in encodings:
                try:
                    with open(test_file, 'r', encoding=encoding) as f:
                        content = f.read(100)  # 读取前100个字符
                        if len(content) > 0:
                            print(f"   ✅ {test_file} 可用 {encoding} 编码读取")
                            print(f"   📝 内容预览: {content[:50]}...")
                            break
                except UnicodeDecodeError:
                    continue
            else:
                print(f"   ❌ {test_file} 无法用支持的编码读取")
        except Exception as e:
            print(f"   ❌ 文件读取失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 HAZOP AI Agent 项目设置检查")
    print("=" * 50)
    
    # 显示基本信息
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")
    print()
    
    # 执行检查
    structure_ok = check_project_structure()
    check_reference_files()
    check_config()
    check_file_encoding()
    
    print("\n" + "=" * 50)
    
    if structure_ok:
        print("✅ 项目结构检查完成，所有必需文件都存在")
        print("\n📋 下一步操作:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 配置API密钥: 复制 .env.example 为 .env 并配置")
        print("3. 运行示例: python example_usage.py")
        print("4. 执行分析: python src/hazop_agent.py --help")
    else:
        print("❌ 项目结构不完整，请检查缺少的文件和目录")
    
    return structure_ok

if __name__ == "__main__":
    main()